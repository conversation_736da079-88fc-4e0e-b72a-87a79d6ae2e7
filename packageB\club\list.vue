<template>
	<view class="recharge flex align-items justify-start flex-column">
		<view class="nav">
			<u-navbar :is-back="true" leftIconColor="#000000" :autoBack="true" bgColor="#ffffff" title="精英俱乐部"
				:titleStyle="{ color: '#000000', fontSize: '34rpx', fontWeight: 'bold' }"></u-navbar>
		</view>
		<view class="box flex justify-start">

			<!-- 标签 -->
			<view class="tab_all">
				<scroll-view class="tabs" scroll-y="true">
					<view v-for="(tab, index) in list" :key="index"
						:class="['tab-item', { active: current === index }, { bottomBorder: (current - 1) === index }]"
						@click="handleClick(index, tab.id)">
						<span>{{ tab.name }}</span>
					</view>
				</scroll-view>
			</view>

			<!-- 热门活动 -->
			<scroll-view class="hot flex flex-column" scroll-y="true" @scrolltolower="onScrolltolower">
				<view class="content flex align-items flex-column">
					<view class="flex flex-column w-100 bbb" v-for="(item, index) in hotList" :key="index"
						@click="detail(item.id)">
						<view style="display: flex;align-items: center;padding: 25rpx;">
							<view>
								<image class="bo" :src="item.logo" style="width: 160rpx;height: 160rpx;" mode="aspectFill"></image>
							</view>
							<view style="padding-left: 25rpx;">
								<view style="font-size: 30rpx;color: #323232;font-weight: 600;">{{ item.name }}</view>
								<view class="ellipsis-two-lines" style="margin-top: 20rpx;font-size: 28rpx;color: #9C9C9C;font-weight: 400;">{{ stripHtmlTags(item.content) }}</view>
							</view>
						</view>
					</view>
				</view>
				<view class="bottom_box flex justify-center align-items" style="height: 1240rpx;width: 100%;"
					v-if="hotList.length == 0">
					<view style="text-align: center;">
						<image src="/static/no.png" style="width: 150rpx;height: 150rpx;"></image>
						<view>暂无数据</view>
					</view>
				</view>
				<u-loadmore v-else :status="loadStatus" />
			</scroll-view>



		</view>

	</view>
</template>

<script>
// import MySwiper from "@/components/fuyu-MixSwiper/fuyu-MixSwiper.vue";
import {
	dateWeek
} from '../../utils/dateFormat'
export default {
	data() {
		return {
			cate_ids: '', //标签id
			loadStatus: 'nomore',
			swiperList: [],
			teacherList: [],
			wqList: [],
			hotList: [],
			unread_number: "",
			keywords: "",
			count: 1,
			current: 0,
			autoplay: true,
			scrollTop: 0,
			dotsShow: true,
			videoList: [],
			currentItemType: "",
			tjShow: true,
			videoAutoplay: false,
			list: [{
				name: "全部",
				id: 0
			}
			],
			limit: 6,
			page: 1,
			show: false,
			formData: {
				name: '',
				mobile: '',
				enterprise_name: '',
				enterprise_addr: '',
				outdoor_sport: '',
			},
		};
	},
	onLoad() {
		//this.resetLists();
		this.getHotList();
		this.getBqList();
		// uni.hideTabBar();

	},
	mounted() {
		//this.videoContext = uni.createVideoContext("myVideo"); //创建视频实例指向video
	},
	onShow() {

	},
	beforeDestroy() {
		//this.autoplay = false;
	},
	computed: {
		// limitedList() {
		//   return this.items.slice(0, 3);
		// }
	},
	onPullDownRefresh() {
		uni.showLoading({
			title: '加载中...'
		});
		this.resetLists();
		this.getHotList();
		setTimeout(() => {
			uni.hideLoading();
			uni.stopPullDownRefresh();
		}, 2000)
	},
	methods: {
		onScrolltolower() {
			if (this.hotList.length < this.count) {
				this.page += 1;
				this.getHotList();
			}
		},
		dateWeeks(e) {
			return dateWeek(e);
		},
		// 重置列表
		resetLists() {
			this.page = 1;
			this.hotList = [];
			this.loadStatus = "loading";
		},
		// 标签点击
		handleClick(index, val) {
			this.current = index;
			this.cate_ids = val;
			this.hotList = [];
			this.page = 1;
			this.limit = 6;
			this.getHotList();
		},
		// 获取标签
		getBqList() {
			uni.$u.http
				.get("/api/school.shop/cate_list", {
					params: {
						page: 1,
						limit: 100
					},
				})
				.then((res) => {
					if (res.code == 1) {
						this.list.push(...res.data.list);
						//初始化默认标签
						// if (this.list.length < 3) {
						//   this.list = [...this.list, ...res.data.list.slice(0,2)];
						// }
					} else {
						uni.showToast({
							title: res.msg,
							icon: "none",
							duration: 2000,
						});
					}
				})
				.catch((error) => {
					uni.showToast({
						title: "请求失败，请稍后再试",
						icon: "none",
						duration: 2000,
					});
				});
		},
		// 获取热门活动 和 课程 
		getHotList() {
			uni.$u.http.get('/api/school.shop/shop_list', {
				params: {
					cate_ids: this.cate_ids,
					page: this.page,
					limit: this.limit,
					type: 2,
					order: 'normal',
					status: 1
				}
			}).then(res => {
				if (res.code == 1) {
					this.count = res.data.list.total
					this.hotList = [...this.hotList, ...res.data.list.data];
					if (this.hotList.length >= res.data.list.total) {
						this.loadStatus = 'nomore';
					} else {
						this.loadStatus = 'loading';
					}
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none',
						duration: 2000
					});
					this.loadStatus = 'loading';
				}
			}).catch(error => {
				console.error('请求失败', error);
				this.loadStatus = 'loading';
			});



		},
		// 跳转详情
		detail(id) {
			uni.navigateTo({
				url: "/packageA/club/info?id=" + id,
			});
		},
		// 去除HTML标签，只保留纯文本
		stripHtmlTags(html) {
			if (!html) return '';
			// 去除HTML标签
			let text = html.replace(/<[^>]*>/g, '');
			// 解码HTML实体
			text = text.replace(/&nbsp;/g, ' ');
			text = text.replace(/&lt;/g, '<');
			text = text.replace(/&gt;/g, '>');
			text = text.replace(/&amp;/g, '&');
			text = text.replace(/&quot;/g, '"');
			text = text.replace(/&#39;/g, "'");
			// 去除多余空格和换行
			text = text.replace(/\s+/g, ' ').trim();
			return text;
		},
	},
};
</script>

<style lang="scss" scoped>
.w-100 {
	width: 100%;
}

.flex {
	display: flex;
}

.justify-center {
	justify-content: center;
}

.space-between {
	justify-content: space-between;
}

.align-items {
	align-items: center;
}

.flex-column {
	flex-flow: column;
}

.justify-start {
	justify-content: start;
}

.mar-top-30 {
	margin-top: 30rpx;
}

.tab_all {
	margin-top: 26rpx;
	position: fixed;
	border-radius: 0rpx 18rpx 0rpx 0rpx;
	left: 0;

	.tabs {
		display: flex;
		flex-flow: column;
		margin-right: 20rpx;
		width: 188rpx;
		height: 100vh;


		/* 优化滚动效果 */
		.tab-item {
			display: flex;
			justify-content: center;
			align-items: center;
			// flex: 0 0 auto;
			width: 100%;
			text-align: center;
			position: relative;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			font-size: 28rpx;
			color: #999999;
			height: 110rpx;
			line-height: 110rpx;
			background: #ffffff;

			span {
				width: 100%;
			}
		}

		.tab-item.active {
			background: transparent;
			font-size: 28rpx;
			color: #323232;
			background: #f7f7f7;
			border-radius: 0 0 0 0;

			&:before {
				content: '';
				width: 10rpx;
				height: 46rpx;
				background: linear-gradient(270deg, #FBF66D 0%, #9CEAA2 100%);
				border-radius: 91rpx 91rpx 91rpx 91rpx;
			}

			+.tab-item {
				border-radius: 0 18rpx 0 0;
			}
		}
	}
}


.recharge {
	width: 750rpx;
	background-color: #f7f7f7;
	height: 100%;
	min-height: 100vh;

	.serch_top {
		display: flex;
		align-items: center;

		.serch_top1 {
			// margin-top: 10rpx;
			// margin-bottom: 30rpx;
			position: relative;
			width: 500rpx;
			background: rgba(245, 245, 245, 0.6);
			border-radius: 32rpx 32rpx 32rpx 32rpx;

			.searchBtn {
				position: absolute;
				width: 133rpx;
				height: 60rpx;
				background: #323232;
				border-radius: 40rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 26rpx;
				color: #FFFFFF;
				display: flex;
				justify-content: center;
				align-items: center;
				top: 0;
				right: 0;
			}
		}

		.imgstop_ye {
			margin-left: 30rpx;
			width: 174rpx;
			height: 76rpx;
			position: absolute;
			right: 0;
		}
	}

	.box {
		width: 100%;
		margin-top: 150rpx;
		height: 100%;

		.hot {
			margin-top: 30rpx;
			// margin-bottom: 160rpx;

			margin-left: 208rpx;
			height: 100dvh;

			.content {
				// height: 462rpx;
				// overflow-x: auto;
				/* 允许横向滚动 */
				// white-space: nowrap;
				width: 512rpx;

				.bbb {
					background: #ffffff;
					margin-top: 30rpx;
					border-radius: 36rpx;
				}

				.right {
					margin-left: 20rpx;
					width: 100%;

					.title {
						width: 95%;
						font-family: PingFang SC, PingFang SC;
						font-weight: bold;
						font-size: 30rpx;
						color: #323232;
					}

					.first-image {
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						font-size: 24rpx;
						color: #9C9C9C;

						span {
							font-family: Source Han Sans CN, Source Han Sans CN;
							font-weight: 400;
							font-size: 26rpx;
							color: #222222;
						}

						.time_tex {
							width: 106rpx;
							height: 42rpx;
							background: #BBFC5B;
							border-radius: 159rpx;
							font-weight: 400;
							font-size: 24rpx;
							color: #323232;
							display: flex;
							justify-content: center;
							align-items: center;
							line-height: 30rpx;
						}
					}

					.row {
						margin: 10rpx 0 26rpx 0;

						.remen {
							// width: 136rpx;
							height: 40rpx;
							background: #ebfaf5;
							border-radius: 4rpx 4rpx 4rpx 4rpx;

							font-family: Source Han Sans CN, Source Han Sans CN;
							font-weight: 400;
							font-size: 24rpx;
							color: #14bc84;
							line-height: 28rpx;
							padding: 2rpx 8rpx;
						}

						.line-colum {
							width: 1rpx;
							height: 32rpx;
							background: #7a7a7a;
							margin: 0 13rpx;
						}

						.name {
							width: 110rpx;
							height: 32rpx;
							font-family: PingFang SC, PingFang SC;
							font-weight: 500;
							font-size: 26rpx;
							color: #7a7a7a;
							line-height: 32rpx;
							margin-left: 12rpx;
						}
					}


					.jigou {
						width: 312rpx;
						// height: 32rpx;
						font-family: PingFang SC, PingFang SC;
						font-weight: 500;
						font-size: 26rpx;
						color: #7a7a7a;
						margin: 4rpx 0 14rpx 0;
					}

					.time {
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						font-size: 26rpx;
						color: #323232;

						.time_tex {
							width: 116rpx;
							height: 42rpx;
							background: #BBFC5B;
							border-radius: 159rpx;
							font-weight: 400;
							font-size: 24rpx;
							color: #323232;
							display: flex;
							justify-content: center;
							align-items: center;
							line-height: 30rpx;
						}

						.time_texs {
							background: #FF4810;
							color: #ffffff;
						}

					}

					.imgs_con_div {
						margin-right: 12rpx;
						width: 150rpx;
						height: 150rpx;
						overflow: hidden;

						&:first-child {
							border-radius: 18rpx 0 0 18rpx;
						}

						&:nth-child(3) {
							border-radius: 0 18rpx 18rpx 0;
						}

						.imgs_con {
							width: 100%;
							height: 100%;
							object-fit: cover;
						}
					}


				}

				.bottom {
					margin: 0 0 32rpx 24rpx;
					width: 100%;

					.toptext {
						width: 60%;

						.smalld {
							margin-left: 0 !important;
						}
					}

					.number {
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						font-size: 26rpx;
						color: #3D3D3D;
						line-height: 30rpx;
					}

					.text {
						font-family: PingFang SC, PingFang SC;
						font-weight: 800;
						font-size: 26rpx;
						color: #7a7a7a;
					}

					.money {
						width: 150rpx;
						font-family: PingFang SC, PingFang SC;
						font-weight: 800;
						font-size: 36rpx;
						color: #ff2323;
						justify-content: flex-end;
					}
				}

				.ovr {
					width: 312rpx;
					background: #ffffff;
					border-radius: 20rpx 20rpx 20rpx 20rpx;
					margin-top: 18rpx;
					margin-right: 24rpx;
				}

				.part {
					width: 162rpx;
					height: 70rpx;
					background: url(@/static/index/shangche.png);
					background-size: 162rpx 70rpx;
					background-repeat: no-repeat;
					font-family: 'YouSheBiaoTiHei';
					font-weight: 400;
					font-size: 42rpx;
					color: #BBFC5B;
				}

				.part1 {
					width: 162rpx;
					height: 70rpx;
					background: url(@/static/index/shangchew.png);
					background-size: 162rpx 70rpx;
					background-repeat: no-repeat;
					font-family: 'YouSheBiaoTiHei';
					font-weight: 400;
					font-size: 42rpx;
					color: #9C9C9C;
				}
			}

			::v-deep .u-loadmore {
				padding-bottom: 20rpx;
			}
		}
	}




}

.white-space {
	overflow: hidden;
	/* 确保超出容器的文本被隐藏 */
	white-space: nowrap;
	/* 确保文本在一行内显示 */
	text-overflow: ellipsis;
	/* 使用省略号表示被截断的文本 */
	width: 100%;
}










.pos {
	position: relative;

	.ab {
		position: absolute;
		right: 0%;
		bottom: 0%;
	}

	.m {
		width: 28rpx;
		height: 42rpx;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: bold;
		font-size: 28rpx;
		color: #ff4810;
	}

	.money {
		width: 68rpx;
		font-family: D-DIN-PRO, D-DIN-PRO;
		font-weight: bold;
		font-size: 36rpx;
		color: #ff4810;
	}

	.no {
		width: 64rpx;
		height: 48rpx;
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: bold;
		font-size: 32rpx;
		color: #ff4810;
		line-height: 38rpx;
	}
}

.bottomBorder {
	border-radius: 0px 0px 18rpx 0;
}
.bo{
	position: relative;
	border-radius: 18rpx;
	overflow: hidden;

	&::before {
		content: '';
		position: absolute;
		top: -4rpx;
		left: -4rpx;
		right: -4rpx;
		bottom: -4rpx;
		background: linear-gradient(270deg, #FBF66D 0%, #9CEAA2 100%);
		border-radius: 22rpx;
		z-index: -1;
	}
}
.ellipsis-two-lines{
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
	
}
</style>